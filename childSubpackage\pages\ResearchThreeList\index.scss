@import "../../../template/more/more.scss";
@import "../../../template/null/null.scss";
@import "../../../template/loading/index.scss";

/* input */
.pages {
  height: 100vh;
  overflow: hidden;
  background: #f7f7f7;
}

/* 确保 hidden 元素不占用空间 */
[hidden] {
  display: none !important;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* input */
.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 28rpx 24rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;
  .search-cancel {
    height: 40rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-family:
      PingFang SC,
      PingFang SC-Regular;
    font-weight: 400;
    text-align: RIGHT;
    color: #20263a;
    padding: 0 28rpx;
    border-left: 1rpx solid #dedede;
  }
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 40rpx;
  height: 40rpx;
}

input {
  caret-color: #e72410;
  color: #74798c;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

/* 最近搜索和浏览历史样式  */
.history_wrap {
  background-color: #f7f7f7;
  padding-top: 20rpx;
}
.page__autofit {
  background-color: #fff;
  padding: 28rpx 0;
}
.page__autofit:nth-child(1) {
  margin-top: 0;
}
.search_b {
  padding-bottom: 0 !important;
}

.his_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx;
  border-bottom: 1px solid #eeeeee;
}

.his_titles {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx;
  border-bottom: 1px solid #eeeeee;
}

.his_title_l {
  font-weight: 400;
  font-size: 28rpx;
  color: #20263a;
}

.his_title_icon {
  width: 36rpx;
  height: 36rpx;
}

.his_content {
  display: flex;
  width: 100%;
  overflow: hidden;
  /* border: 1px solid red; */
  /* height: 168rpx; */
  max-height: 160rpx;
  padding: 0 32rpx;
}

.text-box {
  text-align: justify;
  display: flex;
  flex-wrap: wrap;
  /* text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical; */
}

.his_content_item {
  /* display: inline-flex; */
  background: #f5f6f7;
  border-radius: 8rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
  padding: 8rpx 20rpx;
  min-width: 96rpx;
  max-width: 680rpx;
  height: 56rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  justify-content: center;
  text-align: center;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  color: #74798c;
}

/* 暂无历史搜索 */
.his_content_none {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 104rpx;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

/* 筛选组件 */

/* 缺省页 */
.queshen {
  width: 100%;
}

/* 卡片  */
.card-box {
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-box-text {
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  text-align: center;
  background: #fff;
  border: 1px;
  font-size: 28rpx;
  font-family:
    PingFang SC,
    PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
  border-top: 1px dashed #eeeeee;
}
.his_content1_item1 {
  display: flex;
  align-items: center;
  margin-top: 32rpx;
  height: 36rpx;

  .his_content1_item-l {
    width: 518rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #20263a;
    // 最多一行 超出...
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }

  .his_content1_item-r {
    width: 144rpx;
    height: 34rpx;
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #9b9eac;
    flex-shrink: 0;
  }
}

/* 搜索结果样式 */
.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.company_num {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #f7f7f7;

  .text_left {
    font-size: 28rpx;
    font-weight: 400;
    color: #74798c;

    .color_num {
      color: #e72410;
      font-weight: 600;
    }
  }
}

// 更多加载
.custom-wrapper-class {
  width: 100vw;
  .list_wrp {
    width: 100vw;
  }
  .initial-loading-container {
    width: 100vw;
  }
  .refresh-scroll-container {
    width: 100vw;
  }
}

// AI图片样式
.ai_img {
  width: 750rpx;
  height: 172rpx;
  margin: 20rpx 0;
}

// 浏览历史样式

.his_content1 {
  padding: 0 32rpx 20rpx;

  &_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    height: auto;

    &-l {
      flex: 1;
      font-size: 28rpx;
      font-family:
        PingFang SC,
        PingFang SC-Regular;
      font-weight: 400;
      text-align: LEFT;
      color: #20263a;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    &-r {
      font-size: 28rpx;
      font-family:
        PingFang SC,
        PingFang SC-Regular;
      font-weight: 400;
      text-align: RIGHT;
      color: #9b9eac;
    }
  }
}

// 搜索结果内容样式
.search-content {
  background: #f7f7f7;

  // 内容容器
  .content-container {
    display: flex;
    justify-content: space-between;
    width: 750rpx;
    height: 88rpx;
    background: #fff;
    margin-top: 20rpx;
    padding: 0 32rpx;
  }

  // 左侧 TabBar
  .left-tabbar {
    display: flex;
    height: 100%;
    .tab-item {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 28rpx;
      color: #74798c;
      transition: all 0.3s ease;
      margin-right: 32rpx;
      height: 100%;

      &.active {
        position: relative;
        // 一个顶部横线
        &::before {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 40rpx;
          transform: translateX(-50%);
          height: 4rpx;
          background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
        }
        .tab-text {
          font-weight: 600;
          font-size: 28rpx;
          color: #e72410;
        }
      }

      .tab-text {
        font-size: 26rpx;
        color: #20263a;
      }
    }
  }

  // 右侧图片占位
  .right-image {
    display: flex;
    align-items: center;
    image {
      width: 60rpx;
      height: 60rpx;
    }
    text {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #74798c;
    }
  }

  // 搜索结果列表
  .search-results {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: visible;

    .result-count {
      font-size: 26rpx;
      color: #74798c;
      margin-bottom: 20rpx;
      display: block;
    }

    // 撼地智库列表样式
    .library-list {
      width: 100%;
      height: 100%;
      overflow: visible;

      // 统计信息容器
      .company_num_container {
        position: relative;
        z-index: 100;
      }

      .company_num {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        background: #f7f7f7;

        .text_left {
          font-size: 28rpx;
          font-weight: 400;
          color: #74798c;

          .color_num {
            color: #e72410;
            font-weight: 600;
          }
        }

        // 排序下拉框样式
        .sort-dropdown {
          display: flex;
          align-items: center;
          cursor: pointer;

          .sort-text {
            font-size: 28rpx;
            color: #74798c;
            margin-right: 8rpx;

            &.active {
              color: #e72410;
              font-weight: 600;
            }
          }

          .sort-arrow {
            width: 24rpx;
            height: 24rpx;
            transition: transform 0.3s ease;

            &.rotate {
              transform: rotate(180deg);
            }
          }
        }
      }

      // 排序选项下拉列表
      .sort-options {
        position: absolute;
        top: 100%;
        right: 32rpx;
        width: 296rpx;
        height: 220rpx;
        background: #ffffff;
        box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        z-index: 9999;
        overflow: visible;

        // 白色三角形指向排序文字
        &::before {
          content: "";
          position: absolute;
          top: -8rpx;
          right: 20rpx;
          width: 28rpx;
          height: 16rpx;
          background: #ffffff;
          box-shadow: 0rpx 0rpx 40rpx 0rpx rgba(32, 38, 58, 0.1);
          transform: rotate(45deg);
          border-top-left-radius: 4rpx;
        }

        .sort-option {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 296rpx;
          height: 68rpx;
          background: #ffffff;
          border-radius: 0rpx 0rpx 0rpx 0rpx;
          padding: 0 24rpx;
          box-sizing: border-box;

          &.active {
            .option-text {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 600;
              font-size: 24rpx;
              color: #e72410;
            }
          }

          .option-text {
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #20263a;
          }

          .check-icon {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }

      .card-box {
        margin-top: 20rpx;
      }

      .list_wrp {
        padding: 0;
      }

      // 继承 LibraryReportList 组件的样式
      .library-report-list {
        width: 100%;
        height: 100%;
      }
    }
  }
}

//  <!-- 报告 -->
.report_detail {
  margin-top: 20rpx;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32rpx;
    height: 88rpx;
    background: #fff;
    border-bottom: 1rpx solid #eee;
    text {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #9b9eac;
      &:nth-of-type(1) {
        font-weight: 600;
        font-size: 28rpx;
        color: #20263a;
        margin-right: 10rpx;
      }
      &:nth-of-type(3) {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #e72410;
        padding: 0 4rpx;
      }
    }
  }
  .zt-container {
    width: 100vw;
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;
    height: 216rpx;
    background: #ffffff;

    // 隐藏滚动条（兼容处理）
    &::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      background: transparent;
    }

    .zt-grid {
      height: 100%;
      display: flex;
      flex-direction: row;
      align-items: center; // 垂直居中对齐
      padding: 0 24rpx;
    }

    .zt-item {
      display: flex;
      flex-direction: column; // 图片在上，文字在下
      align-items: center; // 水平居中
      margin-right: 16rpx; // 每个 item 间距
      flex-shrink: 0; // 不允许被压缩，保证横向滚动
      width: 160rpx;
      height: 100%;

      .zt-image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-top: 32rpx;
      }

      .zt-name {
        margin-top: 8rpx;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #20263a;
        /* 关键 ↓↓↓ */
        white-space: normal; /* 允许换行 */
        word-wrap: break-word; /* 长单词/英文自动断行 */
        word-break: break-all; /* 必要时强制换行 */
      }
    }
  }
}
